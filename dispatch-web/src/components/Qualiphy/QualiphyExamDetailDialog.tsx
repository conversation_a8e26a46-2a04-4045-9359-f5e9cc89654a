import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Box,
  TextField,
  Typography,
} from '@material-ui/core';
import StandardDialogTitle from '@/components/StandardDialogTitle';
import SelectItemDialog from '@/components/ProcedureGroup/SelectItemDialog';
import ConfirmDialog from '@/components/ConfirmDialog';
import {
  FullOrganizationFragment,
  QualiphyExamFieldsFragment,
  useUpdateQualiphyExamMutation,
  useDeleteQualiphyExamMutation,
  QualiphyFieldsFragmentDoc,
  UpdateQualiphyExamInput,
  ProcedureDefFieldsFragment,
} from '@/generated/graphql';
import { Alert } from '@material-ui/lab';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import { isEqual } from 'lodash';
import { useSnackbar } from 'notistack';
import EditIcon from '@material-ui/icons/Edit';
import ProcedureCard from '@/components/ProcedureCard';

type Props = {
  integrationId: string | null | undefined;
  fullScreen: boolean;
  organization: FullOrganizationFragment;
  open: boolean;
  exam: QualiphyExamFieldsFragment;
  onClose: () => void;
};

export default function QualiphyExamDetailDialog({
  integrationId,
  fullScreen,
  onClose,
  open,
  organization,
  exam,
}: Props): JSX.Element {
  const procedureDefs = (
    organization.procedureDefs as ProcedureDefFieldsFragment[]
  ).sort((a, b) => a.name.localeCompare(b.name));
  const initialSelectedDefs =
    exam.procedureDefinitions
      ?.map((id) => procedureDefs.find((def) => def.id === id))
      .filter((def): def is ProcedureDefFieldsFragment => def !== undefined) ||
    [];
  const { enqueueSnackbar } = useSnackbar();

  const [selectedDefs, setSelectedDefs] = useState(
    initialSelectedDefs.length > 0 ? initialSelectedDefs : [],
  );
  const [expiresAfter, setExpiresAfter] = useState(exam.expiresAfter);
  const [refills, setRefills] = useState(exam.refills);
  const [deleting, setDeleting] = useState(false);
  const [addDefs, setAddDefs] = useState(false);
  const [errors, setErrors] = useState<string[] | null | undefined>(null);

  const [update, { loading: updateExamLoading }] =
    useUpdateQualiphyExamMutation();
  const [deleteExam, { loading: deleteExamLoading }] =
    useDeleteQualiphyExamMutation({
      update: (cache, updateData) => {
        if (updateData.data?.deleteQualiphyExam) {
          cache.updateFragment(
            {
              id: `QualiphyIntegration:${integrationId}`,
              fragmentName: 'QualiphyFields',
              fragment: QualiphyFieldsFragmentDoc,
            },
            (data) => ({
              ...data,
              exams: data.exams.filter(
                (e: QualiphyExamFieldsFragment) => e.id !== exam.id,
              ),
            }),
          );
        } else if (updateData.errors) {
          setErrors(formatGraphQLErrors(updateData.errors));
        } else {
          setErrors(['Unable to delete exam.']);
        }
        setDeleting(false);
      },
    });
  const loading = updateExamLoading || deleteExamLoading;
  const { archived } = exam;

  const isDirty =
    expiresAfter !== exam.expiresAfter ||
    refills !== exam.refills ||
    !isEqual(initialSelectedDefs, selectedDefs);

  const handleSubmit = async () => {
    if (loading || !exam.id) return;
    const ids = (selectedDefs || []).map((def) => def.id as string);
    const input: UpdateQualiphyExamInput = {
      id: exam.id,
      organizationId: organization.id,
      procedureDefinitionIds: ids,
      refills,
      expiresAfter,
    };

    try {
      const response = await update({ variables: { input } });
      if (response.data) {
        onClose();
        enqueueSnackbar(`${exam.title} successfully updated.`, {
          variant: 'success',
        });
      }
      if (response.errors) {
        setErrors(formatGraphQLErrors(response.errors));
      }
    } catch (err) {
      setErrors(
        formatGraphQLErrors(err.graphQLErrors) ?? [err.message] ?? [
            'Error updating the exam',
          ],
      );
    }
  };

  const handleDelete = async () => {
    if (loading || !exam.id) return;
    try {
      const response = await deleteExam({ variables: { id: exam.id } });
      if (response.data?.deleteQualiphyExam) {
        enqueueSnackbar('Exam successfully deleted.', { variant: 'success' });
        onClose();
      }

      if (response.errors) {
        setErrors(formatGraphQLErrors(response.errors));
        setDeleting(false);
      }
    } catch (err) {
      setErrors(
        formatGraphQLErrors(err.graphQLErrors) ?? [err.message] ?? [
            'Error deleting the exam',
          ],
      );
      setDeleting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      fullScreen={fullScreen}
    >
      <StandardDialogTitle onClose={onClose}>
        {exam.title} Details
      </StandardDialogTitle>
      <DialogContent dividers>
        <Box display="flex" flexWrap="wrap" gridGap="1rem" my={2}>
          <Box minWidth={200} flex="1">
            <TextField
              disabled={archived}
              value={refills}
              onChange={(e) => setRefills(Number(e.target.value))}
              variant="outlined"
              type="number"
              label="# Refills"
              fullWidth
              inputProps={{
                min: '0',
                step: '1',
              }}
            />
          </Box>
          <Box minWidth={200} flex="1">
            <TextField
              disabled={archived}
              value={expiresAfter}
              onChange={(e) => setExpiresAfter(Number(e.target.value))}
              variant="outlined"
              type="number"
              label="Expires after (days)"
              fullWidth
              inputProps={{
                min: '0',
                step: '30',
              }}
            />
          </Box>
        </Box>
        <Box
          my={2}
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography style={{ fontWeight: 'bold' }} color="textPrimary">
            Procedures
          </Typography>
          <Button onClick={() => setAddDefs(true)} startIcon={<EditIcon />}>
            Edit Selection
          </Button>
        </Box>
        <Box
          my={2}
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            minHeight: 60,
          }}
        >
          {(selectedDefs || []).sort((a, b) => a.name.localeCompare(b.name))
            .length ? (
            <Box display="flex" flexDirection="column" gridGap="1rem">
              {(selectedDefs || []).map((def) => (
                <ProcedureCard
                  name={def.name}
                  duration={def.duration}
                  price={def.price}
                />
              ))}
            </Box>
          ) : (
            <Typography
              variant="body2"
              color="textSecondary"
              style={{ textAlign: 'center', padding: '16px' }}
            >
              No procedures selected
            </Typography>
          )}
        </Box>
        {errors &&
          errors.length > 0 &&
          errors.map((e) => (
            <Alert key={e} severity="error">
              {e}
            </Alert>
          ))}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {archived ? (
          <Button
            color="secondary"
            onClick={() => setDeleting(true)}
            variant="outlined"
            disabled={loading}
          >
            Delete
          </Button>
        ) : (
          <Button
            color="primary"
            onClick={handleSubmit}
            variant="outlined"
            disabled={loading || !isDirty}
          >
            Save
          </Button>
        )}
      </DialogActions>
      <ConfirmDialog
        open={deleting}
        onCancel={() => setDeleting(false)}
        title="Are you sure you want to delete this exam?"
        onConfirm={handleDelete}
      />
      {addDefs && (
        <SelectItemDialog
          open={addDefs}
          fullScreen={fullScreen}
          items={procedureDefs}
          selectedItems={selectedDefs ?? []}
          // @ts-expect-error typing
          setSelectedItems={setSelectedDefs}
          setOpen={setAddDefs}
          initialSelection
        />
      )}
    </Dialog>
  );
}
