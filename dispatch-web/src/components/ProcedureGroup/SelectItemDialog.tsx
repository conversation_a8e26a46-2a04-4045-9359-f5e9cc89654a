import {
  ProcedureBaseDefGroupFieldsFragment,
  ProcedureBaseDefFieldsFragment,
  ProcedureDefFieldsFragment,
} from '@/generated/graphql';
import React from 'react';
import {
  Dialog,
  Checkbox,
  Button,
  DialogActions,
  DialogTitle,
  DialogContent,
  FormControlLabel,
  FormGroup,
  Box,
  Divider,
} from '@material-ui/core';

export type Item =
  | Partial<ProcedureBaseDefFieldsFragment>
  | Partial<ProcedureBaseDefGroupFieldsFragment>
  | Partial<ProcedureDefFieldsFragment>;

type Props = {
  items: Item[];
  selectedItems: Item[] | null;
  setSelectedItems: (value: Item[]) => void;
  open: boolean;
  fullScreen: boolean;
  setOpen: (b: boolean) => void;
  initialSelection?: boolean;
};

export default function SelectItemDialog({
  items,
  open,
  fullScreen,
  selectedItems,
  setSelectedItems,
  setOpen,
  initialSelection = false,
}: Props): JSX.Element {
  const [selected, setSelected] = React.useState<string[]>(
    initialSelection && selectedItems
      ? selectedItems.map((item) => item?.id as string)
      : [],
  );

  const handleClick = (id: string) => {
    if (selected.includes(id)) setSelected(selected.filter((i) => i !== id));
    else setSelected([...selected, id]);
  };

  const selectAll = () => {
    const allIds = items.map((item) => item?.id as string);
    setSelected(allIds);
  };

  const clearAll = () => {
    setSelected([]);
  };

  const handleCancel = () => {
    setOpen(false);
    setSelected([]);
  };

  const handleSave = () => {
    const newItems = items.filter((i: Item) =>
      selected.includes(i?.id as string),
    );
    if (initialSelection) {
      setSelectedItems(newItems);
    } else {
      setSelectedItems(
        selectedItems ? [...selectedItems, ...newItems] : newItems,
      );
    }
    handleCancel();
  };

  return (
    <Dialog
      open={open}
      fullScreen={fullScreen}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>Add Items</DialogTitle>
      <DialogContent dividers>
        {items.length > 0 && (
          <Box display="flex" flexDirection="row" mb={2}>
            <Button onClick={selectAll}>Select all</Button>
            <Divider orientation="vertical" flexItem />
            <Button onClick={clearAll}>Clear all</Button>
          </Box>
        )}
        <FormGroup>
          {items.map((i) => (
            <FormControlLabel
              key={i.id}
              control={
                <Checkbox
                  checked={selected.includes(i?.id as string)}
                  onClick={() => handleClick(i?.id as string)}
                />
              }
              label={i.name}
            />
          ))}
        </FormGroup>
        {!items.length && 'No items to select from.'}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>Cancel</Button>
        <Button onClick={handleSave} color="primary" variant="outlined">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
