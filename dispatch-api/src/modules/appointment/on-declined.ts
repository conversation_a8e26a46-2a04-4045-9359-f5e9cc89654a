import { SqlDbSource } from '../../datasources';
import { dispatch } from '../dispatch/service';
import { trackAppointment } from '../twilio/analytics';
import { logAppointment } from './log';
import { Appointment } from './sqldb';

interface OnDeclinedOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onDeclined({
  sqldb,
  appointment,
}: OnDeclinedOptions): Promise<void> {
  trackAppointment({ appointment, sqldb });
  logAppointment(appointment, { sqldb });

  try {
    const constraint = await appointment.$relatedQuery(
      'constraint',
      sqldb.knex,
    );

    if (constraint) {
      dispatch({
        sqldb,
        appointmentRequestId: constraint.requestId,
      });
    }
  } catch (error) {
    console.error('Error in appointment-declined dispatch:', error);
  }
}
