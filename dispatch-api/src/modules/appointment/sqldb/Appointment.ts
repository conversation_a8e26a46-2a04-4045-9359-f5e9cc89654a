import { capitalize, filter } from 'lodash';
import { Model, Pojo, RelationMappings } from 'objection';
import {
  Field,
  Float,
  ID,
  Int,
  ObjectType,
  registerEnumType,
} from 'type-graphql';
import { ArchivableModel, BaseModel } from '../../common/sqldb';
import { formatTimeRange, fullName } from '../../common/util';
import { Checkout } from '../../payment/sqldb';
import { ProcedureBaseDefinition } from '../../procedure/sqldb';
import { QualiphyInvitation } from '../../qualiphy/sqldb';
import AppointmentConstraint from './AppointmentConstraint';
import AppointmentParticipant from './AppointmentParticipant';
import AppointmentTime from './AppointmentTime';
import {
  AppointmentFields,
  AppointmentSortField,
  AppointmentStatus,
  ParticipantType,
} from './types';

registerEnumType(AppointmentStatus, {
  name: 'AppointmentStatus',
});

registerEnumType(AppointmentSortField, {
  name: 'AppointmentSortFields',
});

@ObjectType()
export default class Appointment
  extends ArchivableModel
  implements AppointmentFields
{
  @Field(() => ID)
  readonly id!: number;

  @Field(() => AppointmentStatus)
  status!: AppointmentStatus;

  @Field(() => Date)
  start!: Date;

  @Field(() => Date)
  end!: Date;

  @Field(() => Int, { description: 'Appointment length in minutes' })
  duration!: number;

  @Field(() => String)
  location!: string;

  @Field(() => Float, { nullable: true })
  latitude?: number;

  @Field(() => Float, { nullable: true })
  longitude?: number;

  @Field(() => Date, { nullable: true })
  startedAt?: Date | null;

  @Field(() => Date, { nullable: true })
  completedAt?: Date | null;

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => String, { nullable: true })
  cancelReason?: string;

  @Field(() => ID, { nullable: true })
  createdBy?: number;

  constraintId?: number;

  participants?: AppointmentParticipant[];
  procedureBaseDefs?: ProcedureBaseDefinition[];
  constraint?: AppointmentConstraint;
  alternateTimes?: AppointmentTime[];
  checkout?: Checkout;
  qualiphyInvitation?: QualiphyInvitation;

  checkoutId?: number;

  static tableName = 'appointments';

  static relationMappings = (): RelationMappings => ({
    participants: {
      relation: Model.HasManyRelation,
      modelClass: require('.').AppointmentParticipant,
      join: {
        from: 'appointments.id',
        to: 'appointmentParticipants.appointmentId',
      },
    },
    procedureBaseDefs: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureBaseDefinition,
      modify: 'withArchived',
      join: {
        from: 'appointments.id',
        through: {
          from: 'appointmentsProcedureBaseDefs.appointmentId',
          to: 'appointmentsProcedureBaseDefs.baseDefId',
        },
        to: 'procedureBaseDefinitions.id',
      },
    },
    constraint: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').AppointmentConstraint,
      join: {
        from: 'appointments.constraintId',
        to: 'appointmentConstraints.id',
      },
    },
    alternateTimes: {
      relation: Model.HasManyRelation,
      modelClass: require('.').AppointmentTime,
      join: {
        from: 'appointments.id',
        to: 'appointmentTimes.appointmentId',
      },
    },
    checkout: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../payment/sqldb').Checkout,
      join: {
        from: 'appointments.checkoutId',
        to: 'checkouts.id',
      },
    },
    qualiphyInvitation: {
      relation: Model.HasOneRelation,
      modelClass: require('../../qualiphy/sqldb').QualiphyInvitation,
      join: {
        from: 'appointments.id',
        to: 'qualiphyInvitations.appointmentId',
      },
    },
  });

  $parseDatabaseJson(json: Pojo): Pojo {
    json = super.$parseDatabaseJson(json);
    BaseModel.toDate(json, 'start');
    BaseModel.toDate(json, 'end');
    BaseModel.toDate(json, 'startedAt');
    BaseModel.toDate(json, 'completedAt');
    BaseModel.toNumber(json, 'latitude');
    BaseModel.toNumber(json, 'longitude');
    return json;
  }

  toString(): string {
    const patients = filter(this.participants, {
      type: ParticipantType.PATIENT,
    });

    const practitioners = filter(this.participants, {
      type: ParticipantType.PRACTITIONER,
    });

    const tzid = patients[0]?.clientProfile?.tzid;

    const indicator =
      {
        [AppointmentStatus.PENDING]: '🟠',
        [AppointmentStatus.BOOKED]: '🔵',
        [AppointmentStatus.COMPLETED]: '⚫',
        [AppointmentStatus.CANCELLED]: '🔴',
        [AppointmentStatus.NOSHOW]: '🔴',
      }[this.status] ?? '⚫';

    return [
      `Appointment (id:${this.id})`,
      `Status: ${indicator} ${capitalize(this.status)}`,
      `${formatTimeRange(this.start, this.end, tzid)}`,
      `Practitioner: ${practitioners
        .map((p) => fullName(p.profile?.givenName, p.profile?.familyName))
        .join(', ')}`,
      `Procedures: ${this.procedureBaseDefs
        ?.map((def) => def.name)
        .join(', ')}`,
      `Location: ${this.location}`,
    ].join('\n');
  }
}
