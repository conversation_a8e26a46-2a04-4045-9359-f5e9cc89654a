import { SqlDbSource } from '../../datasources';
import { trackAppointmentRequest } from '../twilio/analytics';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';

interface OnRequestCancelledOptions {
  sqldb: SqlDbSource;
  appointmentRequest: AppointmentRequest;
}

export default async function onRequestCancelled({
  sqldb,
  appointmentRequest,
}: OnRequestCancelledOptions): Promise<void> {
  trackAppointmentRequest({ request: appointmentRequest, sqldb });
  logAppointmentRequest(appointmentRequest, { sqldb });

  // TODO: Send notifications
}
