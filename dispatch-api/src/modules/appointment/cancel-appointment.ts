import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import onCancelled from './on-cancelled';
import { Appointment } from './sqldb';
import { getAppointment } from './sqldb/queries';
import { AppointmentStatus } from './sqldb/types';
import { upsertAppointmentCheckout } from './checkout';
import { completeCheckout } from '../marketplace/checkout';

interface CancelAppointmentParams {
  sqldb: SqlDbSource;
  appointmentId: number;
  refundCheckout?: boolean;
  reason?: string;
}

export async function cancelAppointment(
  params: CancelAppointmentParams,
): Promise<Appointment | null> {
  const { sqldb, appointmentId, refundCheckout = false, reason } = params;

  // lock the appointment

  const release = await acquireLock({
    sqldb,
    resources: [`appointments:${appointmentId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The appointment is temporarily locked',
      'cancel-appointment:locked',
    );
  }

  try {
    const appointment = await getAppointment(sqldb.knex, { id: appointmentId });

    if (!appointment) {
      throw new ApolloError(
        'Invalid appointment',
        'cancel-appointment:appointment',
      );
    }

    if (appointment.status === AppointmentStatus.COMPLETED) {
      throw new ApolloError(
        'The appointment has already been completed',
        'cancel-appointment:completed',
      );
    }

    // TODO: send notifications

    try {
      await Appointment.query(sqldb.knex).findById(appointmentId).patch({
        status: AppointmentStatus.CANCELLED,
        cancelReason: reason,
      });
    } catch (err) {
      throw new ApolloError(
        'Error canceling the appointment',
        'cancel-appointment:error',
      );
    }

    const cancelledAppointment = await getAppointment(sqldb.knex, {
      id: appointmentId,
    });

    if (cancelledAppointment) {
      if (refundCheckout && appointment.checkoutId) {
        try {
          const { checkout } = await upsertAppointmentCheckout({
            sqldb,
            appointment,
            applyPackages: false,
            refund: {
              balance: 0,
              reason: 'Refund - Appointment cancelled',
            },
          });
          if (checkout) {
            await completeCheckout({
              sqldb,
              checkoutId: checkout.id,
              expectedAmount: checkout.balance,
            });
          }
        } catch (err) {
          console.log(err);
        }
      }

      await onCancelled({ sqldb, appointment: cancelledAppointment });
    }

    return cancelledAppointment ?? null;
  } finally {
    await release();
  }
}
